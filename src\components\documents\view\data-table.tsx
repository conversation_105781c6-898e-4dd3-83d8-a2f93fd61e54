// components/table/data-table.tsx
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useUrlSearch } from "@/hooks/use-url-search";
import { Search, X } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";
import { ColumnFilter } from "./column-filter";
import { PaginationControls } from "./pagination-controls";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  description?: string;
  requestEmailer?: string;
}

// Custom filter function for date range
const dateRangeFilter = (
  row: any,
  columnId: string,
  filterValue: DateRange
) => {
  if (!filterValue?.from) return true;

  const cellValue = row.getValue(columnId);
  if (!cellValue) return false;

  const date = new Date(cellValue);
  const from = filterValue.from;
  const to = filterValue.to || filterValue.from;

  return date >= from && date <= to;
};

export function DataTable<TData, TValue>({
  columns,
  data,
  title = "Documents",
  description,
  requestEmailer,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([
    { desc: false, id: "document" },
  ]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({ tags: false });
  const [rowSelection, setRowSelection] = React.useState({});

  const { searchValue, debouncedSearch, updateSearch, clearSearch } =
    useUrlSearch();

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: updateSearch,
    globalFilterFn: "includesString",
    filterFns: {
      dateRange: dateRangeFilter,
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter: debouncedSearch,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
      columnVisibility: {
        tags: false,
      },
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between flex-wrap w-full gap-4">
            <div className="flex items-center space-x-2 flex-wrap">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents, departments, languages, tags..."
                  value={searchValue}
                  onChange={(event) => updateSearch(event.target.value)}
                  className="pl-8 w-sm"
                />
              </div>
              {(table.getState().columnFilters.length > 0 ||
                debouncedSearch) && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    table.resetColumnFilters();
                    clearSearch();
                  }}
                  className="h-8 px-2 lg:px-3"
                >
                  Reset
                  <X className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-4 flex-wrap">
              <Button
                variant="outline"
                onClick={() => {
                  window.open(
                    "https://brain.prd.dia-apps.bosch.tech/brains/an4NImBPKdMl",
                    "_blank"
                  );
                }}
                className="h-8 p-2 lg:px-3"
              >
                Ask your Document Library Assistant
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  location.href = `mailto:${requestEmailer}?subject=Request%20Missing%20Documents&body=Hello%2C%0AI%20would%20like%20to%20ask%20to%20add%20process%20instruction%20to%20the%20central%20library%20for%20the%20following%20topic%28s%29%3A%0A`;
                }}
                className="h-8 px-2 lg:px-3"
              >
                Request Missing Documents here
              </Button>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} className="px-4 py-3">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
                {/* Filter Row */}
                <TableRow className="bg-muted/30">
                  {table.getFlatHeaders().map((header) => {
                    const column = header.column;
                    return (
                      <TableHead key={header.id} className="px-4 py-2">
                        {column.getCanFilter() ? (
                          <ColumnFilter
                            column={column}
                            title={column.id.replace("_", " ")}
                          />
                        ) : null}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="px-4 py-3">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <PaginationControls table={table} />
        </div>
      </CardContent>
    </Card>
  );
}
