# Export Functionality Documentation

## Overview
The export functionality allows users to export document management data to Excel (.xlsx) format from the `/manage` page.

## Features

### Button Placement
- Located above the data table
- Positioned to the left of the "Add Document" button
- Follows existing UI styling patterns with outline variant

### Export Functionality
- Uses the `xlsx` library to generate Excel files
- Exports all currently displayed data (respects active filters and search)
- Includes all visible columns except the "Actions" column

### Excel File Structure
1. **First row**: Title with timestamp (e.g., "Document Management Export - Generated on 12/15/2024 14:30:22")
2. **Second row**: Empty row for spacing
3. **Third row onwards**: Table headers followed by all data rows
4. **Formatting**: Bold headers with gray background, proper column widths

### File Naming
- Format: `DocMng-Data-{timestamp}.xlsx`
- Timestamp format: `YYYYMMDD-HHMMSS` (e.g., "DocMng-Data-20241215-143022.xlsx")

## Implementation Details

### Column Mapping
The export function maps table columns to readable headers:
- `document` → "Document"
- `department` → "Department"
- `language` → "Language"
- `tags` → "Tags"
- `release_date` → "Release Date"
- `revision_date` → "Revision Date"
- `days_until_revision` → "Days Until Revision"
- `view_counter` → "Views"
- `visibile` → "Visible"

### Data Formatting
- **Dates**: Formatted as "MMM DD, YYYY" (e.g., "Dec 15, 2024")
- **Boolean values**: "Yes" or "No" for visibility status
- **Other values**: Exported as-is

### Column Widths
Optimized column widths based on content type:
- Document names: 30 characters
- Tags: 25 characters
- Department/Language: 15 characters
- Dates: 12 characters
- Numbers: 10 characters
- Boolean: 8 characters

### Error Handling
- Shows loading state during export generation
- Validates that data exists before export
- Displays success/error messages via toast notifications
- Gracefully handles export failures

## User Experience

### Loading State
- Button text changes to "Exporting..." during processing
- Button is disabled to prevent multiple simultaneous exports

### Feedback
- Success message: "Export completed successfully"
- Error message: "Export failed. Please try again."
- No data message: "No data to export. Please check your filters."

## Technical Implementation

### Dependencies
- `xlsx` library (already installed in package.json)
- `sonner` for toast notifications
- `lucide-react` for the Download icon

### Key Functions
- `handleExport`: Main export function that processes table data
- Uses TanStack Table's filtered row model to respect current filters
- Generates Excel file with proper formatting and styling

### File Location
- Main implementation: `src/components/documents/manage/data-table.tsx`
- Export button integrated into existing table component

## Usage
1. Navigate to the `/manage` page
2. Apply any desired filters or search terms
3. Click the "Export" button
4. File will automatically download to the user's default download folder

## Future Enhancements
Potential improvements could include:
- Export format selection (CSV, PDF)
- Custom column selection
- Export scheduling
- Email delivery of exports
