// app/api/documents/route.ts
import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { graphqlRequest } from "@/app/helpers/db-util";
import {
  CREATE_DOCUMENT,
  UPDATE_DOCUMENT,
  DELETE_DOCUMENT,
} from "@/app/graphql/mutations";
import { revalidatePath } from "next/cache";

// GET - Fetch documents for management
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession();
    if (!session?.user || !session.isGS || !session.isGsEditor) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (id) {
      // Fetch single document
      const GET_SINGLE_DOCUMENT = `
        query GetSingleDocument($id: String!) {
          documents_by_pk(id: $id) {
            id
            tags
            document
            department
            language
            link
            release_date
            revision_date
            days_until_revision
            visibile
            view_counter
            company
          }
        }
      `;

      const data = await graphqlRequest({
        query: GET_SINGLE_DOCUMENT,
        operationName: "GetSingleDocument",
        variables: { id },
      });

      return NextResponse.json(data.documents_by_pk);
    }

    // Fetch all documents for management
    const GET_MANAGE_DOCUMENTS = `
      query GetManageDocuments {
        documents(
          where: {_and: {company: {_eq: "GS"}}}
          order_by: {document: desc_nulls_last}
        ) {
          id
          tags
          document
          department
          language
          link
          release_date
          revision_date
          days_until_revision
          visibile
          view_counter
          company
        }
      }
    `;

    const data = await graphqlRequest({
      query: GET_MANAGE_DOCUMENTS,
      operationName: "GetManageDocuments",
    });

    return NextResponse.json(data.documents);
  } catch (error) {
    console.error("Error fetching documents:", error);
    return NextResponse.json(
      { error: "Failed to fetch documents" },
      { status: 500 }
    );
  }
}

// POST - Create new document
export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || !session.isGS || !session.isGsEditor) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();

    // Calculate revision date based on release date and days until revision
    const releaseDate = new Date(body.release_date);
    const revisionDate = new Date(releaseDate);
    revisionDate.setDate(
      releaseDate.getDate() + (body.days_until_revision || 912)
    );

    const documentInput = {
      ...body,
      revision_date: revisionDate.toISOString().split("T")[0],
      view_counter: 0,
      company: "GS",
    };

    const data = await graphqlRequest({
      query: CREATE_DOCUMENT,
      operationName: "CreateDocument",
      variables: { input: documentInput },
    });

    revalidatePath("/");

    return NextResponse.json(data.insert_documents_one);
  } catch (error) {
    console.error("Error creating document:", error);
    return NextResponse.json(
      { error: "Failed to create document" },
      { status: 500 }
    );
  }
}

// PUT - Update document
export async function PUT(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || !session.isGS || !session.isGsEditor) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Calculate revision date if release date or days until revision changed
    if (updateData.release_date || updateData.days_until_revision) {
      const releaseDate = new Date(updateData.release_date);
      const revisionDate = new Date(releaseDate);
      revisionDate.setDate(
        releaseDate.getDate() + (updateData.days_until_revision || 912)
      );
      updateData.revision_date = revisionDate.toISOString().split("T")[0];
    }

    const data = await graphqlRequest({
      query: UPDATE_DOCUMENT,
      operationName: "UpdateDocument",
      variables: { id, input: updateData },
    });

    revalidatePath("/");

    return NextResponse.json(data.update_documents_by_pk);
  } catch (error) {
    console.error("Error updating document:", error);
    return NextResponse.json(
      { error: "Failed to update document" },
      { status: 500 }
    );
  }
}

// DELETE - Delete document
export async function DELETE(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || !session.isGS || !session.isGsEditor) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = Number(searchParams.get("id"));

    if (!id) {
      return NextResponse.json(
        { error: "Document ID is required" },
        { status: 400 }
      );
    }

    const data = await graphqlRequest({
      query: DELETE_DOCUMENT,
      operationName: "DeleteDocument",
      variables: { id },
    });

    revalidatePath("/");

    return NextResponse.json(data.delete_documents_by_pk);
  } catch (error) {
    console.error("Error deleting document:", error);
    return NextResponse.json(
      { error: "Failed to delete document" },
      { status: 500 }
    );
  }
}
