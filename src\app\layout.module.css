.custom_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #eff1f2;
  min-height: 100vh;
}

.header {
  width: 100%;
  background-color: white;
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main {
  display: flex;
  flex-grow: 1;
  width: 100%;
  flex-direction: column;
  align-items: start;
  background-color: #eff1f2;
  margin-top: 24px;
  margin-bottom: 32px;
}

.content_wrapper {
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media (width <= 1440px) {
    padding: 0 24px;
  }
}
