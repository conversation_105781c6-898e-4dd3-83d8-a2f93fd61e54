// lib/validations/document.ts
import { z } from "zod";

export const documentFormSchema = z.object({
  tags: z.string().min(1, "Tags are required"),
  document: z.string().min(1, "Document name is required"),
  department: z.string().min(1, "Department is required"),
  language: z.string().min(1, "Language is required"),
  link: z.string().min(1, "Link is required"),
  release_date: z.string().min(1, "Release date is required"),
  days_until_revision: z.string().min(1, "Days until revision is required"),
  visibile: z.boolean(),
});

export type DocumentFormData = z.infer<typeof documentFormSchema>;

// Schema for updating documents (all fields required)
export const updateDocumentSchema = z.object({
  id: z.string().min(1, "Document ID is required"),
  tags: z.string().min(1, "Tags are required"),
  document: z.string().min(1, "Document name is required"),
  department: z.string().min(1, "Department is required"),
  language: z.string().min(1, "Language is required"),
  link: z.string().min(1, "Link is required"),
  release_date: z.string().min(1, "Release date is required"),
  days_until_revision: z.string().min(1, "Days until revision is required"),
  visibile: z.boolean(),
});

export type UpdateDocumentData = z.infer<typeof updateDocumentSchema>;
