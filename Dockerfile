FROM devtimrepo.emea.bosch.com:8223/node:21-alpine as base
ENV HTTP_PROXY http://************:3128
ENV HTTPS_PROXY http://************:3128
RUN apk add --no-cache g++ make py3-pip libc6-compat nano
WORKDIR /app
COPY package*.json ./
EXPOSE 3000

FROM base as builder
WORKDIR /app
COPY . .
ENV HTTP_PROXY http://************:3128
ENV HTTPS_PROXY http://************:3128
ENV NO_PROXY 127.0.0.1,localhost,************,timrhdev02.emea.bosch.com,*.bosch.com
RUN npm install
RUN npm run build

# Production stage
FROM base as production
WORKDIR /app
ENV NODE_ENV=production
RUN npm ci
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs
# Copy built assets from the builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.ts ./next.config.ts
COPY --from=builder /app/public ./public
# Start the application
CMD ["npm", "start"]

# Development stage
FROM base as development
ENV NODE_ENV=development
RUN npm install
COPY . .
CMD ["npm", "run", "dev"]
