"use client";

import { ManageDocument } from "@/app/types/manage-document";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useDocumentAction } from "@/hooks/use-document-action";
import { ColumnDef } from "@tanstack/react-table";
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Download,
  Edit,
  ExternalLink,
  Trash2,
} from "lucide-react";

const getSortIcon = (isSorted: string | false) => {
  if (isSorted === "asc") {
    return <ArrowUp className="ml-2 h-4 w-4" />;
  }
  if (isSorted === "desc") {
    return <ArrowDown className="ml-2 h-4 w-4" />;
  }
  return <ArrowUpDown className="ml-2 h-4 w-4" />;
};

interface ActionsProps {
  document: ManageDocument;
  onEdit: (document: ManageDocument) => void;
  onDelete: (document: ManageDocument) => void;
}

function ActionsCell({ document, onEdit, onDelete }: ActionsProps) {
  return (
    <div className="flex items-center gap-2">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onEdit(document)}
        className="h-8 w-8 p-0"
        title="Edit document"
      >
        <Edit className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => onDelete(document)}
        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
        title="Delete document"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}

function DocumentNameCell({ document }: { document: ManageDocument }) {
  const { handleDocumentClick, isProcessing, getActionIcon, getActionText } =
    useDocumentAction();

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    await handleDocumentClick({
      documentId: document.id,
      link: document.link,
      documentName: document.document,
    });
  };

  const iconType = getActionIcon(document.link);
  const actionText = getActionText(document.link);
  const IconComponent = iconType === "download" ? Download : ExternalLink;

  return (
    <Button
      variant="link"
      className="h-auto p-0 font-medium text-primary hover:text-primary/80 justify-start w-fit w-max-[500px]"
      onClick={handleClick}
      disabled={isProcessing}
      title={`${actionText}: ${document.document}`}
    >
      <IconComponent className="mr-2 h-4 w-4" />
      <span className="truncate">
        {isProcessing ? "Processing..." : document.document}
      </span>
    </Button>
  );
}

interface ColumnsProps {
  onEdit: (document: ManageDocument) => void;
  onDelete: (document: ManageDocument) => void;
}

export const createColumns = ({
  onEdit,
  onDelete,
}: ColumnsProps): ColumnDef<ManageDocument>[] => [
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <ActionsCell
        document={row.original}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "document",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Document
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => <DocumentNameCell document={row.original} />,
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Department
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <Badge variant="secondary" className="font-medium">
        {row.getValue("department")}
      </Badge>
    ),
  },
  {
    accessorKey: "language",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Language
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <Badge variant="outline" className="font-medium">
        {row.getValue("language")}
      </Badge>
    ),
  },
  {
    accessorKey: "tags",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Tags
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate text-sm text-muted-foreground"
        title={row.getValue("tags")}
      >
        {row.getValue("tags")}
      </div>
    ),
  },
  {
    accessorKey: "link",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Path
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[300px] truncate text-sm text-muted-foreground font-mono"
        title={row.getValue("link")}
      >
        {row.getValue("link")}
      </div>
    ),
    enableHiding: true,
  },
  {
    accessorKey: "release_date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Release Date
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("release_date"));
      return (
        <div className="font-medium">
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    filterFn: "dateRange" as any,
  },
  {
    accessorKey: "revision_date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Revision Date
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("revision_date"));
      return (
        <div className="font-medium">
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    filterFn: "dateRange" as any,
  },
  {
    accessorKey: "days_until_revision",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Days Until Revision
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue("days_until_revision")}
      </div>
    ),
    filterFn: "daysUntilRevision" as any,
  },
  {
    accessorKey: "view_counter",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Views
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <div className="text-center font-medium">
        {row.getValue("view_counter")}
      </div>
    ),
  },
  {
    accessorKey: "visibile",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Visible
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => {
      const isVisible = row.getValue("visibile");
      return (
        <Badge variant={isVisible ? "default" : "secondary"}>
          {isVisible ? "Yes" : "No"}
        </Badge>
      );
    },
    filterFn: "visibility" as any,
  },
];
