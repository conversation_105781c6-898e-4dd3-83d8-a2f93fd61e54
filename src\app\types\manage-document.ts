export interface ManageDocument {
  id: string;
  tags: string;
  document: string;
  department: string;
  language: string;
  link: string;
  release_date: string;
  revision_date: string;
  days_until_revision: number;
  visibile: boolean;
  view_counter: number;
  company: string;
}

export interface DocumentFormData {
  tags: string;
  document: string;
  department: string;
  language: string;
  link: string;
  release_date: string;
  revision_date: string;
  days_until_revision: number;
  visibile: boolean;
}
