// app/api/documents/download/route.ts
import { NextRequest, NextResponse } from "next/server";
import { readFile, access, constants } from "fs/promises";
import {
  convertWindowsPathToLinux,
  convertToGSDocsPath,
  getFileNameFromPath,
  getMimeType,
} from "@/lib/file-utils";
import { getSession } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const session = await getSession();
    if (!session?.user || !session.isGS) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { filePath } = body;

    if (!filePath) {
      return NextResponse.json(
        { error: "File path is required" },
        { status: 400 }
      );
    }

    // Try both encoded and decoded versions of the file path
    const originalFilePath = filePath;
    const decodedFilePath = decodeURIComponent(filePath);

    console.log(`Original path: ${originalFilePath}`);
    console.log(`Decoded path: ${decodedFilePath}`);

    // Helper function to check file existence and return the valid path
    async function findValidPath(basePath: string): Promise<string | null> {
      const pathsToTry = [
        basePath, // Try original encoded path first
        decodeURIComponent(basePath), // Try decoded path as fallback
      ];

      for (const pathToTry of pathsToTry) {
        try {
          await access(pathToTry, constants.F_OK);
          console.log(`File found at: ${pathToTry}`);
          return pathToTry;
        } catch (error) {
          console.log(`File not found at: ${pathToTry}`);
          continue;
        }
      }
      return null;
    }

    // Try to find the file in gsdocs folder
    const gsDocsPathOriginal = convertToGSDocsPath(originalFilePath);
    const gsDocsPathDecoded = convertToGSDocsPath(decodedFilePath);

    console.log(`Checking gsdocs paths:`);
    console.log(`- Original: ${gsDocsPathOriginal}`);
    console.log(`- Decoded: ${gsDocsPathDecoded}`);

    let finalPath = await findValidPath(gsDocsPathOriginal);

    // If not found in gsdocs with original path, try with decoded path
    if (!finalPath && gsDocsPathOriginal !== gsDocsPathDecoded) {
      finalPath = await findValidPath(gsDocsPathDecoded);
    }

    if (!finalPath) {
      console.error(
        `File not found in gsdocs folder. Tried paths:`,
        `\n- Original: ${gsDocsPathOriginal}`,
        `\n- Decoded: ${gsDocsPathDecoded}`
      );
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    console.log(`Successfully found file at: ${finalPath}`);

    const fileBuffer = await readFile(finalPath);

    // Get filename for Content-Disposition header
    // Use decoded version for display purposes (cleaner filename in browser)
    const fileName = getFileNameFromPath(decodedFilePath);
    const mimeType = getMimeType(fileName);

    const response = new NextResponse(fileBuffer as any);

    response.headers.set("Content-Type", mimeType);
    response.headers.set(
      "Content-Disposition",
      `attachment; filename="${fileName}"`
    );
    response.headers.set("Content-Length", fileBuffer.length.toString());

    return response;
  } catch (error) {
    console.error("Download error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
