import { getSession } from "@/lib/auth";
import { FileBadge, FileEdit, Home } from "lucide-react";
import Link from "next/link";
import UserInfo from "./UserInfo";

export default async function Header() {
  const session = await getSession();
  if (!session) return <p>No session</p>;

  return (
    <header className="w-full bg-white border-b shadow-sm">
      <div className="max-w-[1440px] mx-auto flex justify-between items-center px-4 py-3">
        <nav className="flex items-center gap-6">
          <Link
            href="https://timapps.emea.bosch.com/"
            className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors"
          >
            <Home size={18} />
            <span className="text-sm font-medium">Home</span>
          </Link>

          {session?.isGS && (
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors"
            >
              <FileBadge size={18} />
              <span className="text-sm font-medium">
                Documents Management (GS)
              </span>
            </Link>
          )}

          {session?.isGsEditor && (
            <Link
              href="/manage"
              className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors"
            >
              <FileEdit size={18} />
              <span className="text-sm font-medium">Manage Documents</span>
            </Link>
          )}
        </nav>

        {session && <UserInfo username={session.user?.name || ""} />}
      </div>
    </header>
  );
}
