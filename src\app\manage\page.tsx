// app/manage/page.tsx
"use client";

import { ManageDocument } from "@/app/types/manage-document";
import { ManageDocumentsTable } from "@/components/documents/manage/manage-documents-table";
import { Card, CardContent } from "@/components/ui/card";
import { RefreshCw } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function ManagePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [documents, setDocuments] = useState<ManageDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/signin");
      return;
    }

    const hasGSAccess = session.isGS && session.isGsEditor;
    if (!hasGSAccess) {
      toast.error("You don't have permission to access this page");
      router.push("/");
      return;
    }
  }, [session, status, router]);

  const fetchDocuments = async () => {
    try {
      const response = await fetch("/api/documents");
      if (!response.ok) {
        throw new Error("Failed to fetch documents");
      }
      const data = await response.json();
      setDocuments(data);
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast.error("Failed to load documents");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (session && session?.isGS && session.isGsEditor) {
      fetchDocuments();
    }
  }, [session]);

  if (status === "loading" || isLoading) {
    return (
      <main className="min-h-screen w-full flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="pt-6">
            <div className="flex items-center justify-center space-x-2">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>Loading...</span>
            </div>
          </CardContent>
        </Card>
      </main>
    );
  }

  if (!session?.user || !session?.isGS || !session?.isGsEditor) {
    return null;
  }

  return (
    <main className="min-h-screen w-full">
      <div className="container mx-auto pb-6">
        <ManageDocumentsTable data={documents} onRefresh={fetchDocuments} />
      </div>
    </main>
  );
}
