import {
  CREATE_DOCUMENT,
  DELETE_DOCUMENT,
  UPDATE_DOCUMENT,
} from "@/app/graphql/mutations";
import {
  GET_MANAGE_DOCUMENTS,
  GET_VIEW_DOCUMENTS,
} from "@/app/graphql/queries";

export const graphqlRequest = async ({
  query,
  variables = {},
  operationName,
}) => {
  try {
    const graphqlQuery = {
      operationName: operationName || null,
      query,
      variables,
    };

    const response = await fetch(process.env.HASURA_ENDPOINT, {
      method: "POST",
      headers: {
        "x-hasura-admin-secret": process.env.HASURA_SECRET,
      },
      body: JSON.stringify(graphqlQuery),
    });

    const result = await response.json();

    if (result.errors) {
      throw new Error(
        result.errors.map((error, index) => {
          return `Error ${index + 1}: ${error.message}.`;
        })
      );
    }

    return result.data;
  } catch (error) {
    console.error("GraphQL Request Error:", error);
    throw new Error(error.message || "An error occurred while fetching data.");
  }
};

export const getViewDocuments = async () => {
  const data = await graphqlRequest({
    query: GET_VIEW_DOCUMENTS,
    operationName: "GetViewDocuments",
  });

  return data.documents;
};

export const getManageDocuments = async () => {
  const data = await graphqlRequest({
    query: GET_MANAGE_DOCUMENTS,
    operationName: "GetManageDocuments",
  });

  return data.documents;
};

export const createDocument = async (documentData) => {
  // Calculate revision date based on release date and days until revision
  const releaseDate = new Date(documentData.release_date);
  const revisionDate = new Date(releaseDate);
  revisionDate.setDate(
    releaseDate.getDate() + (documentData.days_until_revision || 912)
  );

  const documentInput = {
    ...documentData,
    revision_date: revisionDate.toISOString().split("T")[0],
    view_counter: 0,
    company: "GS",
  };

  const data = await graphqlRequest({
    query: CREATE_DOCUMENT,
    operationName: "CreateDocument",
    variables: { input: documentInput },
  });

  return data.insert_documents_one;
};

export const updateDocument = async (id, updateData) => {
  // Calculate revision date if release date or days until revision changed
  if (updateData.release_date || updateData.days_until_revision) {
    const releaseDate = new Date(updateData.release_date);
    const revisionDate = new Date(releaseDate);
    revisionDate.setDate(
      releaseDate.getDate() + (updateData.days_until_revision || 912)
    );
    updateData.revision_date = revisionDate.toISOString().split("T")[0];
  }

  const data = await graphqlRequest({
    query: UPDATE_DOCUMENT,
    operationName: "UpdateDocument",
    variables: { id, input: updateData },
  });

  return data.update_documents_by_pk;
};

export const deleteDocument = async (id) => {
  const data = await graphqlRequest({
    query: DELETE_DOCUMENT,
    operationName: "DeleteDocument",
    variables: { id },
  });

  return data.delete_documents_by_pk;
};

export async function updateDocumentViewCounter(documentId) {
  const UPDATE_VIEW_COUNTER = `
    mutation UpdateViewCounter($id: Int!) {
      update_documents_by_pk(
        pk_columns: { id: $id }
        _inc: { view_counter: 1 }
      ) {
        id
        view_counter
      }
    }
  `;

  try {
    const data = await graphqlRequest({
      query: UPDATE_VIEW_COUNTER,
      operationName: "UpdateViewCounter",
      variables: {
        id: documentId,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to update view counter:", error);
    throw error;
  }
}
