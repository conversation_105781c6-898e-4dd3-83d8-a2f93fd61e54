import { getSession } from "@/lib/auth";
import { Metadata } from "next";
import "./globals.css";
import classes from "./layout.module.css";
import { AlertOctagon } from "lucide-react";
import { Toaster } from "@/components/ui/sonner";
import AuthSessionProvider from "./providers/AuthSessionProvider";
import SignInPage from "./signin/page";
import Graphic from "@/components/layout/Graphic";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "DocMng",
  description: "Documents Management Application",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getSession();
  let buildTimestamp;
  let roTimestamp;
  if (process.env.NEXT_PUBLIC_BUILD_TIMESTAMP) {
    buildTimestamp = new Date(process.env.NEXT_PUBLIC_BUILD_TIMESTAMP);
    roTimestamp = buildTimestamp.toLocaleString("ro-RO", {
      timeZone: "Europe/Bucharest",
    });
  }

  const showBanner = process.env?.SHOW_BANNER === "true";

  if (!session?.user) {
    return (
      <html lang="en">
        <body>
          <AuthSessionProvider session={session}>
            <SignInPage />
          </AuthSessionProvider>
        </body>
      </html>
    );
  }

  return (
    <html lang="en">
      <body>
        <div className={classes.custom_container}>
          <Graphic />
          <Header />

          <AuthSessionProvider session={session}>
            <main className={classes.main}>
              <div className={classes.content_wrapper}>
                {showBanner && (
                  <header className="flex flex-row justify-between bg-orange-200 p-4 text-fuchsia-600 w-full rounded-lg mb-5">
                    <AlertOctagon className="mr-4" />
                    Development system! Do not use for production purposes!
                    <AlertOctagon className="ml-4" />
                  </header>
                )}

                {children}
              </div>
            </main>
          </AuthSessionProvider>

          <footer className="w-full text-gray-500 text-end px-2 py-1 items-center text-xs">
            App {process.env.NODE_ENV} version:{" "}
            {process.env?.NEXT_PUBLIC_APP_VERSION} @ {roTimestamp}
          </footer>
        </div>
        <Toaster />
      </body>
    </html>
  );
}
