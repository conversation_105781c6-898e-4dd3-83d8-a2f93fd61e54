import { getViewDocuments } from "@/app/helpers/db-util";
import { DocumentsTable } from "@/components/documents/view/documents-table";
import { getSession } from "@/lib/auth";
import Link from "next/link";

export default async function Home() {
  const session = await getSession();

  if (!session?.user || !session.isGS) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          Not authorized
        </h2>
        <p className="text-gray-600 mb-6">
          You don’t have permission to access this page.
        </p>
        <Link
          href="https://timapps.emea.bosch.com/"
          className="px-4 py-2 rounded-lg bg-[#007bc0] text-white hover:bg-[#005a9c] transition-colors"
        >
          Go to Home Page
        </Link>
      </div>
    );
  }
  const documents = await getViewDocuments();
  const requestEmailer = process.env.REQUEST_EMAILER ?? "";

  return (
    <main className="min-h-screen w-7xl">
      <DocumentsTable data={documents} requestEmailer={requestEmailer} />
    </main>
  );
}
