"use client";

import classes from "./SignIn.module.css";
import { signIn, useSession } from "next-auth/react";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

const SignInPage = () => {
  const { status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      signIn(
        "azure-ad",
        {
          callbackUrl: process.env.AZURE_CALLBACK_URL,
        },
        { prompt: "select_account" }
      );
    }
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  return (
    <div className={classes.login_container}>
      {status === "loading" ? (
        <h1>Loading.....</h1>
      ) : (
        <h1>Please wait while you are being redirected...</h1>
      )}
    </div>
  );
};

export default SignInPage;
