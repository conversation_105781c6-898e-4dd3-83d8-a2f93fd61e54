// components/documents-table.tsx
"use client";

import { ViewDocument } from "@/app/types/document";
import { DataTable } from "./data-table";
import { columns } from "./columns";

interface DocumentsTableProps {
  data: ViewDocument[];
  requestEmailer?: string;
}

export function DocumentsTable({ data, requestEmailer }: DocumentsTableProps) {
  return (
    <div className="container mx-auto pb-6 mt-2">
      <DataTable
        columns={columns}
        data={data}
        title="Procedures, Guidelines, Work Instructions, Forms"
        requestEmailer={requestEmailer}
      />
    </div>
  );
}
