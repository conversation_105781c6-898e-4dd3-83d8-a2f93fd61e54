// app/api/documents/view-counter/route.ts
import { NextRequest, NextResponse } from "next/server";
import { getSession } from "@/lib/auth";
import { updateDocumentViewCounter } from "@/app/helpers/db-util";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getSession();
    if (!session?.user || !session.isGS) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { documentId } = body;

    if (!documentId) {
      return NextResponse.json(
        { error: "Document ID is required" },
        { status: 400 }
      );
    }

    // Convert documentId to number if it's a string
    const numericDocumentId = typeof documentId === 'string' ? parseInt(documentId, 10) : documentId;
    
    if (isNaN(numericDocumentId)) {
      return NextResponse.json(
        { error: "Invalid document ID" },
        { status: 400 }
      );
    }

    // Update the view counter
    await updateDocumentViewCounter(numericDocumentId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating view counter:", error);
    return NextResponse.json(
      { error: "Failed to update view counter" },
      { status: 500 }
    );
  }
}
