// components/documents/manage/manage-documents-table.tsx
"use client";

import { ManageDocument } from "@/app/types/manage-document";
import { useState } from "react";
import { toast } from "sonner";
import { createColumns } from "./columns";
import { ManageDataTable } from "./data-table";
import { DeleteDocumentDialog } from "./delete-document-dialog";
import { EditDocumentDialog } from "./edit-document-dialog";

interface ManageDocumentsTableProps {
  data: ManageDocument[];
  onRefresh?: () => void;
}

export function ManageDocumentsTable({
  data,
  onRefresh,
}: ManageDocumentsTableProps) {
  const [editingDocument, setEditingDocument] = useState<ManageDocument | null>(
    null
  );
  const [deletingDocument, setDeletingDocument] =
    useState<ManageDocument | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleEdit = (document: ManageDocument) => {
    setEditingDocument(document);
    setIsEditDialogOpen(true);
  };

  const handleDelete = (document: ManageDocument) => {
    setDeletingDocument(document);
    setIsDeleteDialogOpen(true);
  };

  const handleAddNew = () => {
    setEditingDocument(null);
    setIsEditDialogOpen(true);
  };

  const handleEditSuccess = () => {
    setIsEditDialogOpen(false);
    setEditingDocument(null);
    toast.success(
      editingDocument
        ? "Document updated successfully"
        : "Document created successfully"
    );
    onRefresh?.();
  };

  const handleDeleteSuccess = () => {
    setIsDeleteDialogOpen(false);
    setDeletingDocument(null);
    toast.success("Document deleted successfully");
    onRefresh?.();
  };

  const columns = createColumns({
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <>
      <div className="container mx-auto pb-6 mt-2">
        <ManageDataTable
          columns={columns}
          data={data}
          title="Document Management"
          description="Manage your document library"
          onAddNew={handleAddNew}
        />
      </div>

      <EditDocumentDialog
        document={editingDocument}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSuccess={handleEditSuccess}
      />

      <DeleteDocumentDialog
        document={deletingDocument}
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onSuccess={handleDeleteSuccess}
      />
    </>
  );
}
