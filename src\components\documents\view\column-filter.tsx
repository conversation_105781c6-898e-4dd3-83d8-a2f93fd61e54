// components/table/column-filter.tsx
"use client";

import { Column } from "@tanstack/react-table";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";

interface ColumnFilterProps<TData, TValue> {
  column: Column<TData, TValue>;
  title?: string;
}

export function ColumnFilter<TData, TValue>({
  column,
  title,
}: ColumnFilterProps<TData, TValue>) {
  const columnId = column.id;

  // Date columns - use DateRange picker
  if (columnId === "release_date" || columnId === "revision_date") {
    const filterValue = column.getFilterValue() as DateRange | undefined;

    return (
      <DatePickerWithRange
        date={filterValue}
        onDateChange={(dateRange) => {
          column.setFilterValue(dateRange);
        }}
        placeholder={`Filter by ${title?.toLowerCase() || "date"}...`}
        className="w-full max-w-xs"
      />
    );
  }

  // Visibility column - use dropdown select
  if (columnId === "visibile") {
    const filterValue = (column.getFilterValue() as string) ?? "";

    return (
      <Select
        value={filterValue}
        onValueChange={(value) => {
          column.setFilterValue(value === "all" ? "" : value);
        }}
      >
        <SelectTrigger className="max-w-sm">
          <SelectValue placeholder="Filter by visibility..." />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All</SelectItem>
          <SelectItem value="true">Yes</SelectItem>
          <SelectItem value="false">No</SelectItem>
        </SelectContent>
      </Select>
    );
  }

  // Days until revision column - use text input with string matching
  if (columnId === "days_until_revision") {
    return (
      <Input
        placeholder="Filter by days..."
        value={(column.getFilterValue() as string) ?? ""}
        onChange={(event) => {
          // Convert the numeric value to string for substring matching
          column.setFilterValue(event.target.value);
        }}
        className="max-w-sm"
        type="text"
      />
    );
  }

  // Default - text input for other columns
  return (
    <Input
      placeholder={`Filter by ${title?.toLowerCase() || column.id}...`}
      value={(column.getFilterValue() as string) ?? ""}
      onChange={(event) => column.setFilterValue(event.target.value)}
      className="max-w-sm"
    />
  );
}
