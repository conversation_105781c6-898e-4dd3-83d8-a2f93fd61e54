// components/documents/manage/edit-document-dialog.tsx
"use client";

import { ManageDocument } from "@/app/types/manage-document";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  documentFormSchema,
  type DocumentFormData,
} from "@/lib/validations/document";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

interface EditDocumentDialogProps {
  document?: ManageDocument | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function EditDocumentDialog({
  document,
  open,
  onOpenChange,
  onSuccess,
}: EditDocumentDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const isEditing = !!document;

  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentFormSchema),
    defaultValues: {
      tags: "",
      document: "",
      department: "",
      language: "",
      link: "",
      release_date: "",
      days_until_revision: "912",
      visibile: true,
    },
  });

  useEffect(() => {
    if (open) {
      if (document) {
        const releaseDate = document.release_date
          ? document.release_date.split("T")[0]
          : "";

        form.reset({
          tags: document.tags || "",
          document: document.document || "",
          department: document.department || "",
          language: document.language || "",
          link: document.link || "",
          release_date: releaseDate,
          days_until_revision: String(document.days_until_revision || 912),
          visibile: document.visibile ?? true,
        });
      } else {
        form.reset({
          tags: "",
          document: "",
          department: "",
          language: "",
          link: "",
          release_date: "",
          days_until_revision: "912",
          visibile: true,
        });
      }
    }
  }, [open, document, form]);

  const onSubmit = async (data: DocumentFormData) => {
    setIsLoading(true);
    try {
      // Convert days_until_revision from string to number and validate
      const daysUntilRevision = parseInt(data.days_until_revision, 10);
      if (isNaN(daysUntilRevision) || daysUntilRevision < 1) {
        toast.error(
          "Days until revision must be a valid number greater than 0"
        );
        return;
      }

      // Prepare the data with converted number
      const processedData = {
        ...data,
        days_until_revision: daysUntilRevision,
      };

      const url = "/api/documents";
      const method = isEditing ? "PUT" : "POST";
      const body = isEditing
        ? { id: document.id, ...processedData }
        : processedData;

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to save document");
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving document:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to save document"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="mb-2">
          <DialogTitle>
            {isEditing ? "Edit Document" : "Add New Document"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="document"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Document Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter document name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter department" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="language"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter language (e.g., EN, DE, RO)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="release_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Release Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="days_until_revision"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Days Until Revision</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" placeholder="912" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="link"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Link</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter document link or file path"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter tags separated by commas"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="visibile"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Visible</FormLabel>
                    <FormDescription>
                      Make this document visible to users
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update" : "Create"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
