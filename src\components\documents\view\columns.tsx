// components/table/columns.tsx
"use client";

import { ViewDocument } from "@/app/types/document";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useDocumentAction } from "@/hooks/use-document-action";
import { ColumnDef } from "@tanstack/react-table";
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Download,
  ExternalLink,
} from "lucide-react";

const getSortIcon = (isSorted: string | false) => {
  if (isSorted === "asc") {
    return <ArrowUp className="ml-2 h-4 w-4" />;
  }
  if (isSorted === "desc") {
    return <ArrowDown className="ml-2 h-4 w-4" />;
  }
  return <ArrowUpDown className="ml-2 h-4 w-4" />;
};

function DocumentNameCell({ document }: { document: ViewDocument }) {
  const { handleDocumentClick, isProcessing, getActionIcon, getActionText } =
    useDocumentAction();

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    await handleDocumentClick({
      documentId: document.id,
      link: document.link,
      documentName: document.document,
    });
  };

  const iconType = getActionIcon(document.link);
  const actionText = getActionText(document.link);
  const IconComponent = iconType === "download" ? Download : ExternalLink;

  return (
    <Button
      variant="link"
      className="h-auto p-0 font-medium text-primary hover:text-primary/80 justify-start w-[500px]"
      onClick={handleClick}
      disabled={isProcessing}
      title={`${actionText}: ${document.document}`}
    >
      <IconComponent className="mr-2 h-4 w-4" />
      <span className="truncate">
        {isProcessing ? "Processing..." : document.document}
      </span>
    </Button>
  );
}

export const columns: ColumnDef<ViewDocument>[] = [
  {
    accessorKey: "document",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Document
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => <DocumentNameCell document={row.original} />,
    sortingFn: (rowA, rowB, columnId) => {
      const a = rowA.getValue<string>(columnId) || "";
      const b = rowB.getValue<string>(columnId) || "";
      return a.localeCompare(b, undefined, {
        numeric: true,
        sensitivity: "base",
      });
    },
  },
  {
    accessorKey: "department",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Department
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <Badge variant="secondary" className="font-medium">
        {row.getValue("department")}
      </Badge>
    ),
  },
  {
    accessorKey: "language",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Language
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => (
      <Badge variant="outline" className="font-medium">
        {row.getValue("language")}
      </Badge>
    ),
  },
  {
    accessorKey: "release_date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Release Date
        {getSortIcon(column.getIsSorted())}
      </Button>
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("release_date"));
      return (
        <div className="font-medium">
          {date.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </div>
      );
    },
    filterFn: "dateRange" as any,
  },
  {
    accessorKey: "tags",
    header: "Tags",
    cell: ({ row }) => row.getValue("tags"),
    enableHiding: true,
  },
];
