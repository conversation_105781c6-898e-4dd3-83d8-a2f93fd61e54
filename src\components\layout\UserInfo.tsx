"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ChevronDown, LogOut, User } from "lucide-react";
import { signOut } from "next-auth/react";

type UserInfoProps = {
  username: string;
};

export default function UserInfo({ username }: UserInfoProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <button className="flex items-center gap-3 text-sm font-medium hover:text-gray-600 cursor-pointer">
          <User size={30} />
          <span>Hello, {username}!</span>
          <ChevronDown className="w-5 h-5" />
        </button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-fit p-2">
        <Button
          variant="ghost"
          className="w-full text-left py-1 flex gap-4 justify-between cursor-pointer"
          onClick={() => signOut()}
        >
          <LogOut />
          Sign Out
        </Button>
      </PopoverContent>
    </Popover>
  );
}
