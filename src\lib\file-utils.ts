// lib/file-utils.ts
import path from "path";

/**
 * Determines if a link is a URL or a file path
 */
export function isUrl(link: string): boolean {
  if (!link) return false;

  // Normalize input (trim spaces)
  const value = link.trim().toLowerCase();

  // Common links
  if (
    value.startsWith("http://") ||
    value.startsWith("https://") ||
    value.startsWith("ftp://") ||
    value.startsWith("www.")
  ) {
    return true;
  }

  return false;
}

/**
 * Determines if a link is a file path (Windows UNC or local path)
 */
export function isFilePath(link: string): boolean {
  // Check for Windows UNC paths
  if (
    link.startsWith("file://") ||
    link.startsWith("\\\\") ||
    link.startsWith("//") ||
    /^[a-zA-Z]:[\\\/]/.test(link)
  ) {
    // C:\ or C:/ pattern
    return true;
  }

  // If it's not a URL, assume it's a file path
  return !isUrl(link);
}

/**
 * Converts file path to gsdocs folder path
 * For files that should be served from the gsdocs/ folder in project root
 */
export function convertToGSDocsPath(filePath: string): string {
  // Extract just the filename from the path
  const fileName = getFileNameFromPath(filePath);

  // Return path to gsdocs folder in project root
  return path.join(process.cwd(), "gsdocs", fileName);
}

/**
 * Converts Windows UNC path to Linux container path
 * Example: file://///info01/Administrativ$/Informationsforemployees/Library/07_HR/LFR-13153-950_BUSINESS.doc
 * Becomes: /GS_documents/Administrativ$/Informationsforemployees/Library/07_HR/LFR-13153-950_BUSINESS.doc
 */
export function convertWindowsPathToLinux(windowsPath: string): string {
  // Remove file:// protocol prefix
  let cleanPath = windowsPath.replace(/^file:\/\/+/, "");

  // Remove server name (everything up to the first $ or meaningful folder)
  // Pattern: \\server\share$ or //server/share$
  cleanPath = cleanPath.replace(
    /^[\\\/]*[^\\\/]+[\\\/]+([^\\\/]+\$[\\\/]*)/,
    "$1"
  );

  // Convert backslashes to forward slashes
  cleanPath = cleanPath.replace(/\\/g, "/");

  // Prepend the container documents path
  const containerPath = path.join("/GS_documents", cleanPath);

  return containerPath;
}

/**
 * Extracts filename from path
 */
export function getFileNameFromPath(filePath: string): string {
  const cleanPath = filePath.replace(/^file:\/\/+/, "");
  const parts = cleanPath.split(/[\\\/]/);
  return parts[parts.length - 1] || "download";
}

/**
 * Gets MIME type based on file extension
 */
export function getMimeType(filename: string): string {
  const ext = path.extname(filename).toLowerCase();

  const mimeTypes: Record<string, string> = {
    ".pdf": "application/pdf",
    ".doc": "application/msword",
    ".docx":
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ".xls": "application/vnd.ms-excel",
    ".xlsx":
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".ppt": "application/vnd.ms-powerpoint",
    ".pptx":
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ".txt": "text/plain",
    ".csv": "text/csv",
    ".zip": "application/zip",
    ".png": "image/png",
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".gif": "image/gif",
  };

  return mimeTypes[ext] || "application/octet-stream";
}
