"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

/**
 * Custom hook for managing search state with URL synchronization
 * Provides bidirectional sync between URL search params and local state
 */
export function useUrlSearch() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Get initial search value from URL
  const initialSearch = searchParams.get("search") || "";
  const [searchValue, setSearchValue] = useState(initialSearch);
  const [debouncedSearch, setDebouncedSearch] = useState(initialSearch);

  // Debounce search input to avoid excessive URL updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchValue);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Update URL when debounced search changes
  useEffect(() => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));
    
    if (debouncedSearch) {
      current.set("search", debouncedSearch);
    } else {
      current.delete("search");
    }

    const search = current.toString();
    const query = search ? `?${search}` : "";
    
    // Only update URL if it's different from current
    if (query !== `?${searchParams.toString()}`) {
      router.replace(`${pathname}${query}`, { scroll: false });
    }
  }, [debouncedSearch, pathname, router, searchParams]);

  // Update local state when URL changes (e.g., browser back/forward, direct navigation)
  useEffect(() => {
    const urlSearch = searchParams.get("search") || "";
    if (urlSearch !== searchValue) {
      setSearchValue(urlSearch);
      setDebouncedSearch(urlSearch);
    }
  }, [searchParams, searchValue]);

  // Function to update search value
  const updateSearch = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  // Function to clear search
  const clearSearch = useCallback(() => {
    setSearchValue("");
  }, []);

  return {
    searchValue,
    debouncedSearch,
    updateSearch,
    clearSearch,
  };
}
