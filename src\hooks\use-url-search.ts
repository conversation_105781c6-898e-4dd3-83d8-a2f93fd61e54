"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";

/**
 * Custom hook for managing search state with URL synchronization
 * Provides bidirectional sync between URL search params and local state
 */
export function useUrlSearch() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get initial search value from URL
  const initialSearch = searchParams.get("search") || "";
  const [searchValue, setSearchValue] = useState(initialSearch);
  const [debouncedSearch, setDebouncedSearch] = useState(initialSearch);

  // Use ref to track if we're updating from URL to prevent circular updates
  const isUpdatingFromUrl = useRef(false);

  // Debounce search input to avoid excessive URL updates
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchValue);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Update URL when debounced search changes (only if not updating from URL)
  useEffect(() => {
    if (isUpdatingFromUrl.current) {
      isUpdatingFromUrl.current = false;
      return;
    }

    const currentUrlSearch = searchParams.get("search") || "";

    // Only update URL if the debounced search is different from current URL
    if (debouncedSearch !== currentUrlSearch) {
      const current = new URLSearchParams(Array.from(searchParams.entries()));

      if (debouncedSearch) {
        current.set("search", debouncedSearch);
      } else {
        current.delete("search");
      }

      const search = current.toString();
      const query = search ? `?${search}` : "";

      router.replace(`${pathname}${query}`, { scroll: false });
    }
  }, [debouncedSearch, pathname, router, searchParams]);

  // Update local state when URL changes (e.g., browser back/forward, direct navigation)
  useEffect(() => {
    const urlSearch = searchParams.get("search") || "";

    // Only update if URL search is different from current search value
    if (urlSearch !== searchValue) {
      isUpdatingFromUrl.current = true;
      setSearchValue(urlSearch);
      setDebouncedSearch(urlSearch);
    }
  }, [searchParams]);

  const updateSearch = useCallback((value: string) => {
    setSearchValue(value);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchValue("");
  }, []);

  return {
    searchValue,
    debouncedSearch,
    updateSearch,
    clearSearch,
  };
}
