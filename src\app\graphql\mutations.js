// GraphQL mutation for creating a new document
export const CREATE_DOCUMENT = `
  mutation CreateDocument($input: documents_insert_input!) {
    insert_documents_one(object: $input) {
      id
      tags
      document
      department
      language
      link
      release_date
      revision_date
      days_until_revision
      visibile
      view_counter
      company
    }
  }
`;

// GraphQL mutation for updating a document
export const UPDATE_DOCUMENT = `
  mutation UpdateDocument($id: Int!, $input: documents_set_input!) {
    update_documents_by_pk(pk_columns: { id: $id }, _set: $input) {
      id
      tags
      document
      department
      language
      link
      release_date
      revision_date
      days_until_revision
      visibile
      view_counter
      company
    }
  }
`;

// GraphQL mutation for deleting a document
export const DELETE_DOCUMENT = `
  mutation DeleteDocument($id: Int!) {
    delete_documents_by_pk(id: $id) {
      id
    }
  }
`;

// GraphQL mutation for updating view counter
export const UPDATE_VIEW_COUNTER = `
  mutation UpdateViewCounter($id: Int!) {
    update_documents_by_pk(
      pk_columns: { id: $id }
      _inc: { view_counter: 1 }
    ) {
      id
      view_counter
    }
  }
`;
