// components/documents/manage/data-table.tsx
"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useUrlSearch } from "@/hooks/use-url-search";
import { Download, Plus, Search, X } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";
import { toast } from "sonner";
import * as XLSX from "xlsx";
import { ColumnFilter } from "../view/column-filter";
import { PaginationControls } from "../view/pagination-controls";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  title?: string;
  description?: string;
  onAddNew?: () => void;
}

// Custom filter function for date range
const dateRangeFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: DateRange
) => {
  if (!filterValue?.from) return true;

  const cellValue = row.getValue(columnId);
  if (!cellValue) return false;

  const date = new Date(cellValue);
  const from = filterValue.from;
  const to = filterValue.to || filterValue.from;

  return date >= from && date <= to;
};

// Custom filter function for visibility column
const visibilityFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: string
) => {
  if (!filterValue || filterValue === "all") return true;

  const cellValue = row.getValue(columnId);
  const booleanValue = Boolean(cellValue);

  return filterValue === "true" ? booleanValue : !booleanValue;
};

// Custom filter function for days until revision (numeric string matching)
const daysUntilRevisionFilter: FilterFn<any> = (
  row: any,
  columnId: string,
  filterValue: string
) => {
  if (!filterValue) return true;

  const cellValue = row.getValue(columnId);
  if (cellValue === null || cellValue === undefined) return false;

  // Convert the numeric value to string and check if it includes the filter value
  const stringValue = String(cellValue);
  return stringValue.includes(filterValue);
};

export function ManageDataTable<TData, TValue>({
  columns,
  data,
  title = "Manage Documents",
  description = "Manage your document library with full CRUD operations",
  onAddNew,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([
    { desc: false, id: "document" },
  ]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [isExporting, setIsExporting] = React.useState(false);

  // Use URL search hook for global filter
  const { searchValue, debouncedSearch, updateSearch, clearSearch } =
    useUrlSearch();
  console.log(searchValue);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onGlobalFilterChange: updateSearch,
    globalFilterFn: "includesString",
    filterFns: {
      dateRange: dateRangeFilter,
      visibility: visibilityFilter,
      daysUntilRevision: daysUntilRevisionFilter,
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      globalFilter: debouncedSearch,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  });

  const handleExport = React.useCallback(async () => {
    try {
      setIsExporting(true);

      const exportRows = table.getSortedRowModel().rows;
      const visibleColumns = table
        .getVisibleFlatColumns()
        .filter((col) => col.id !== "actions");

      if (exportRows.length === 0) {
        toast.error("No data to export. Please check your filters.");
        return;
      }

      const wb = XLSX.utils.book_new();

      const now = new Date();
      const timestamp = now.toLocaleString("ro-RO", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      });

      const titleRow = [
        `Document Management Export - Generated on ${timestamp}`,
      ];

      const headerRow = visibleColumns.map((column) => {
        const header = column.columnDef.header;
        if (typeof header === "string") return header;
        return column.id.charAt(0).toUpperCase() + column.id.slice(1);
      });

      const dataRows = exportRows.map((row) => {
        return visibleColumns.map((column) => {
          const value = row.getValue(column.id);

          if (column.id === "release_date" || column.id === "revision_date") {
            return new Date(value as string).toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
            });
          }

          if (column.id === "visibile") {
            return value ? "Yes" : "No";
          }

          return value;
        });
      });

      const allRows = [titleRow, [], headerRow, ...dataRows];
      const ws = XLSX.utils.aoa_to_sheet(allRows);

      const colWidths = visibleColumns.map((column) => {
        switch (column.id) {
          case "document":
            return { wch: 30 };
          case "tags":
            return { wch: 25 };
          case "department":
          case "language":
            return { wch: 15 };
          case "release_date":
          case "revision_date":
            return { wch: 12 };
          case "days_until_revision":
          case "view_counter":
            return { wch: 10 };
          case "visibile":
            return { wch: 8 };
          default:
            return { wch: 20 };
        }
      });
      ws["!cols"] = colWidths;

      XLSX.utils.book_append_sheet(wb, ws, "Documents");

      const fileTimestamp = now
        .toLocaleString("ro-RO", {
          month: "2-digit",
          day: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          hour12: false,
        })
        .replace(/[:]/g, "-")
        .replace(/[,]/g, "");
      const filename = `DocMng-Data-${fileTimestamp}.xlsx`;

      XLSX.writeFile(wb, filename);

      toast.success("Export completed successfully");
    } catch (error) {
      console.error("Export failed:", error);
      toast.error("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
    }
  }, [table]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between flex-wrap w-full gap-4">
            <div className="flex items-center space-x-2 flex-wrap">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search all columns..."
                  value={searchValue}
                  onChange={(event) => updateSearch(event.target.value)}
                  className="pl-8 w-sm"
                />
              </div>
              {(table.getState().columnFilters.length > 0 ||
                debouncedSearch) && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    table.resetColumnFilters();
                    clearSearch();
                  }}
                  className="h-8 px-2 lg:px-3"
                >
                  Reset
                  <X className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>

            <div className="flex items-center gap-4 flex-wrap">
              <Button
                onClick={handleExport}
                disabled={isExporting}
                variant="outline"
                className="h-8 px-3"
              >
                <Download className="mr-2 h-4 w-4" />
                {isExporting ? "Exporting..." : "Export"}
              </Button>
              {onAddNew && (
                <Button onClick={onAddNew} className="h-8 px-3">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Document
                </Button>
              )}
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id} className="px-4 py-3">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
                {/* Filter Row */}
                <TableRow className="bg-muted/30">
                  {table.getFlatHeaders().map((header) => {
                    const column = header.column;
                    return (
                      <TableHead key={header.id} className="px-4 py-2">
                        {column.getCanFilter() ? (
                          <ColumnFilter
                            column={column}
                            title={column.id.replace("_", " ")}
                          />
                        ) : null}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-muted/50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="px-4 py-3">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          <PaginationControls table={table} />
        </div>
      </CardContent>
    </Card>
  );
}
