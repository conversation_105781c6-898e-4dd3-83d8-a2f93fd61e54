"use client";

import { useState } from "react";
import { isUrl, isFilePath } from "@/lib/file-utils";

interface DocumentAction {
  documentId?: string;
  link: string;
  documentName: string;
}

export function useDocumentAction() {
  const [isProcessing, setIsProcessing] = useState(false);

  const updateViewCounter = async (documentId: string) => {
    const response = await fetch("/api/documents/view-counter", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ documentId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update view counter");
    }

    return response.json();
  };

  const handleDocumentClick = async ({
    documentId,
    link,
    documentName,
  }: DocumentAction) => {
    if (isProcessing) return;

    // Update view counter for all document clicks (if documentId is provided)
    if (documentId) {
      try {
        await updateViewCounter(documentId);
      } catch (error) {
        console.error("Failed to update view counter:", error);
        // Continue with the action even if view counter update fails
      }
    }

    // Determine if it's a URL or file path
    if (isUrl(link)) {
      // Navigate to URL
      window.open(link, "_blank", "noopener,noreferrer");
      return;
    } else if (isFilePath(link)) {
      // Download file
      await downloadDocument({ documentId, filePath: link, documentName });
    } else {
      // Fallback: try to open as URL first, then attempt download
      console.warn(
        "Unable to determine link type, attempting to open as URL:",
        link
      );
      window.open(link, "_blank", "noopener,noreferrer");
    }
  };

  const downloadDocument = async ({
    documentId,
    filePath,
    documentName,
  }: {
    documentId?: string;
    filePath: string;
    documentName: string;
  }) => {
    if (isProcessing) return;

    setIsProcessing(true);

    try {
      const response = await fetch("/api/documents/download", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documentId,
          filePath,
          documentName,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Download failed");
      }

      // Get the filename from response headers or use the document name
      const contentDisposition = response.headers.get("Content-Disposition");
      let filename = documentName;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob from response
      const blob = await response.blob();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const linkElement = document.createElement("a");
      linkElement.href = url;
      linkElement.download = filename;

      // Trigger download
      document.body.appendChild(linkElement);
      linkElement.click();

      // Cleanup
      document.body.removeChild(linkElement);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download error:", error);

      // Show user-friendly error message
      const message =
        error instanceof Error ? error.message : "Download failed";

      // You can replace this with your preferred toast notification
      alert(`Download failed: ${message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const getActionType = (link: string): "download" | "navigate" | "unknown" => {
    if (isUrl(link)) return "navigate";
    if (isFilePath(link)) return "download";
    return "unknown";
  };

  const getActionIcon = (link: string) => {
    const actionType = getActionType(link);
    return actionType === "download" ? "download" : "external-link";
  };

  const getActionText = (link: string) => {
    const actionType = getActionType(link);
    switch (actionType) {
      case "download":
        return "Download";
      case "navigate":
        return "Open Link";
      default:
        return "Open";
    }
  };

  return {
    handleDocumentClick,
    downloadDocument,
    isProcessing,
    getActionType,
    getActionIcon,
    getActionText,
  };
}
